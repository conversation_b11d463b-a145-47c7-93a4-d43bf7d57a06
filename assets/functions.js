/**
 * VietFulfil Fulfillment Switcher j<PERSON><PERSON><PERSON>
 * <PERSON><PERSON> lý toggle cho các switcher fulfillment
 */
jQuery(document).ready(function($) {

    // Khởi tạo fulfillment switchers
    function initFulfillmentSwitchers() {
        const $switchers = $('.b-fulfillment-info__switcher-input');
        const $switcherContainers = $('.b-fulfillment-info__switcher');

        if ($switchers.length === 0) {
            return; // Không có switchers trên trang này
        }

        // Fix duplicate IDs nếu có
        fixDuplicateIds($switchers);

        // Bind events
        bindSwitcherEvents($switchers, $switcherContainers);

        // Initialize status
        updateFulfillmentStatus($switchers);

        console.log('VietFulfil: Fulfillment switchers initialized');
    }

    // Fix duplicate IDs
    function fixDuplicateIds($switchers) {
        const usedIds = [];

        $switchers.each(function(index) {
            const $input = $(this);
            const currentId = $input.attr('id');

            if (usedIds.includes(currentId) || !currentId) {
                // Tạo ID mới dựa trên label text
                const $label = $input.closest('.b-fulfillment-info__switcher').find('label[for]');
                const labelText = $label.text().toLowerCase().replace(/\s+/g, '-');
                const newId = `js-b-fulfillment-info__switcher-input--${labelText}`;

                $input.attr('id', newId);
                $label.attr('for', newId);

                console.log(`VietFulfil: Fixed duplicate ID from "${currentId}" to "${newId}"`);
            } else {
                usedIds.push(currentId);
            }
        });
    }

    // Bind events cho switchers
    function bindSwitcherEvents($switchers, $switcherContainers) {

        // Click event cho checkbox
        $switchers.on('change', function() {
            const $checkbox = $(this);
            const $container = $checkbox.closest('.b-fulfillment-info__switcher');
            const $label = $container.find('label[for="' + $checkbox.attr('id') + '"]');
            const countryName = $label.text().trim();

            // Animation effect
            animateSwitcher($container, $checkbox.is(':checked'));

            // Update status
            updateFulfillmentStatus($switchers);

            // Trigger custom event
            $(document).trigger('vietfulfil:switcher:changed', {
                country: countryName,
                checked: $checkbox.is(':checked'),
                element: $checkbox[0]
            });

            console.log(`VietFulfil: ${countryName} ${$checkbox.is(':checked') ? 'enabled' : 'disabled'}`);
        });

        // Click event cho label text (để toggle khi click vào text)
        $switcherContainers.find('label[for]').on('click', function(e) {
            // jQuery sẽ tự động handle việc toggle checkbox
            // Chúng ta chỉ cần prevent double-click
            e.preventDefault();
            const $label = $(this);
            const targetId = $label.attr('for');
            const $checkbox = $('#' + targetId);

            if ($checkbox.length) {
                $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
            }
        });
    }

    // Animation cho switcher
    function animateSwitcher($container, isChecked) {
        // Scale animation
        $container.css('transform', 'scale(1.02)');

        setTimeout(function() {
            $container.css('transform', 'scale(1)');
        }, 150);

        // Optional: Add glow effect
        if (isChecked) {
            $container.addClass('switcher-activated');
            setTimeout(function() {
                $container.removeClass('switcher-activated');
            }, 300);
        }
    }

    // Update status display
    function updateFulfillmentStatus($switchers) {
        const selectedCountries = [];

        $switchers.each(function() {
            const $checkbox = $(this);
            if ($checkbox.is(':checked')) {
                const $label = $checkbox.closest('.b-fulfillment-info__switcher').find('label[for="' + $checkbox.attr('id') + '"]');
                const countryName = $label.text().trim();
                selectedCountries.push(countryName);
            }
        });

        // Update status text nếu có element
        const $statusText = $('#fulfillment-status-text, .fulfillment-status');
        if ($statusText.length) {
            if (selectedCountries.length > 0) {
                $statusText.text('Đã chọn: ' + selectedCountries.join(', '));
            } else {
                $statusText.text('Chưa chọn quốc gia nào');
            }
        }

        // Store in localStorage
        localStorage.setItem('vietfulfil_selected_countries', JSON.stringify(selectedCountries));

        // Trigger custom event
        $(document).trigger('vietfulfil:status:updated', {
            countries: selectedCountries,
            count: selectedCountries.length
        });
    }

    // Load saved state từ localStorage
    function loadSavedState($switchers) {
        const savedCountries = localStorage.getItem('vietfulfil_selected_countries');

        if (savedCountries) {
            try {
                const countries = JSON.parse(savedCountries);

                $switchers.each(function() {
                    const $checkbox = $(this);
                    const $label = $checkbox.closest('.b-fulfillment-info__switcher').find('label[for="' + $checkbox.attr('id') + '"]');
                    const countryName = $label.text().trim();

                    if (countries.includes(countryName)) {
                        $checkbox.prop('checked', true);
                    }
                });

                console.log('VietFulfil: Loaded saved state:', countries);
            } catch (e) {
                console.warn('VietFulfil: Could not load saved state');
            }
        }
    }

    // Public API
    window.VietFulfilSwitcher = {
        // Get selected countries
        getSelected: function() {
            const selected = [];
            $('.b-fulfillment-info__switcher-input:checked').each(function() {
                const $label = $(this).closest('.b-fulfillment-info__switcher').find('label[for="' + $(this).attr('id') + '"]');
                selected.push($label.text().trim());
            });
            return selected;
        },

        // Set selected countries
        setSelected: function(countries) {
            $('.b-fulfillment-info__switcher-input').each(function() {
                const $checkbox = $(this);
                const $label = $checkbox.closest('.b-fulfillment-info__switcher').find('label[for="' + $checkbox.attr('id') + '"]');
                const countryName = $label.text().trim();

                $checkbox.prop('checked', countries.includes(countryName));
            });
            updateFulfillmentStatus($('.b-fulfillment-info__switcher-input'));
        },

        // Toggle all
        toggleAll: function(state) {
            $('.b-fulfillment-info__switcher-input').prop('checked', state).trigger('change');
        }
    };

    // Initialize khi DOM ready
    initFulfillmentSwitchers();

    // Re-initialize khi có AJAX content load
    $(document).on('vietfulfil:content:loaded', function() {
        initFulfillmentSwitchers();
    });

});