// JavaScript để handle switching logic
document.addEventListener('DOMContentLoaded', function () {
    const switchers = document.querySelectorAll('.b-fulfillment-info__switcher-input');
    const statusText = document.getElementById('status-text');

    function updateStatus() {
        const selectedCountries = [];
        switchers.forEach(switcher => {
            if (switcher.checked) {
                const label = document.querySelector(`label[for="${switcher.id}"]`);
                selectedCountries.push(label.textContent);
            }
        });

        if (selectedCountries.length > 0) {
            statusText.textContent = `Đã chọn: ${selectedCountries.join(', ')}`;
        } else {
            statusText.textContent = 'Chưa chọn quốc gia nào';
        }
    }

    // Add event listeners
    switchers.forEach(switcher => {
        switcher.addEventListener('change', function () {
            updateStatus();

            // Optional: Add animation effect
            this.closest('.b-fulfillment-info__switcher').style.transform = 'scale(1.02)';
            setTimeout(() => {
                this.closest('.b-fulfillment-info__switcher').style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Initialize status
    updateStatus();
});