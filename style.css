/*
Theme Name: Vietfulfil
Description: Vietfulfil official website template, design & development by Fluxthemes
Author: Fluxthemes.com
Template: flatsome
Version: 3.0
*/


/* ----------------------------------------------------
1. GENERAL
----------------------------------------------------- */
:root {
    --color-primary: #c62027 !important;
    --color-heading: #21364f !important;
    --color-text: #54595f !important;
    --color-dark: #21364f !important;
    --fs-primary-color: #c62027 !important;
    --font-primary: 'Roboto', sans-serif !important;
}

body {
    font-family: var(--font-primary);
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--color-heading);
}

@media (min-width: 1024px) {
    h1 {
        font-size: 52px;
    }

    h2,
    .h2 {
        font-size: 2rem;
    }
}

.text-primary {
    color: var(--color-primary) !important;
}

.text-body {
    color: var(--color-text) !important;
}

.mb-1 {
    margin-bottom: 16px;
}

.mb-2 {
    margin-bottom: 24px;
}

.box-shadow-3,
.box-shadow-3-hover:hover,
.row-box-shadow-3 .col-inner,
.row-box-shadow-3-hover .col-inner:hover {
    transition: transform .3s, box-shadow .3s, background-color .3s, color .3s, opacity .3s;
    transition-delay: 0s !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, .19), 0 6px 6px rgba(0, 0, 0, .22);
    background-color: var(--color-primary);
}

.box-shadow-3:hover {
    transform: translateY(-6px);
}

/* ----------------------------------------------------
2. HEADER
----------------------------------------------------- */
.header-wrapper {
    box-shadow: 1px 1px 10px rgba(0, 0, 0, .15);
}

.header-top {
    background-color: var(--color-primary) !important;
}

.header-nav-main>li {
    margin: 0 20px;
}

.header-nav-main>li>a {
    font-size: 16px;
    font-weight: 700;
    color: var(--color-heading) !important;
}

.header-nav-main>li.active>a,
.header-nav-main>li:hover>a {
    color: var(--color-primary) !important;
}

.nav-line-bottom>li.active>a:before,
.nav-line-bottom>li:hover>a:before {
    background-color: var(--color-primary) !important;
}

.nav-dropdown-has-shadow .nav-dropdown {
    border: none;
    border-radius: 8px;
    box-shadow: 1px 1px 15px rgba(0, 0, 0, .15);
}

.nav-dropdown.nav-dropdown-default>li>a {
    font-size: 16px;
    font-weight: 600;
}

main
/* ----------------------------------------------------
3. CONTENT
----------------------------------------------------- */


.s2 .box {
    padding: 15px;
    border-radius: 5px 5px 5px 5px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .15);
}

.s2 .box-image {
    min-height: 130px;
    display: flex;
    align-items: center;
}

.s2 .box-text {
    position: initial;
    border-top: 1px solid #eaeaea;
    min-height: 102px;
}

.s2 .box-text h4 {
    position: absolute;
    left: 15px;
    top: 15px;
    text-align: left;
    color: #516f90;
    font-size: 14px;
    font-weight: 600;
}

.s3 {
    border-bottom: 1px solid #eaeaea;
}

.s3 .box {
    margin: 5px;
    padding: 15px;
    border-style: solid;
    border-width: 5px 0 0;
    border-color: #ff7a59;
    border-radius: 5px 5px 5px 5px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .15);
    font-size: 13px;
}

.s3 h2 {
    margin: 25px 0;
}

.s3 .box h4 {
    font-size: 14px;
    color: #21364F;
    margin-bottom: 10px;
}

.s3-right .medium-6 {
    padding-bottom: 20px;
}

.s3-right .medium-6:nth-child(2) .box {
    border-color: #fbb648;
}

.s3-right .medium-6:nth-child(3) .box {
    border-color: #00a4bd;
}

.s4-left li {
    margin: 0 0 5px 0 !important;
    font-size: 16px;
    list-style: none;
}

.s4-left li:before {
    content: "";
    speak: none !important;
    display: inline-block;
    font-display: block;
    font-family: fl-icons !important;
    font-style: normal !important;
    font-variant: normal !important;
    font-weight: 400 !important;
    line-height: 1.2;
    margin: 0;
    padding: 0;
    position: relative;
    text-transform: none !important;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #ff7a59;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: -2px 5px 0 0;
    font-size: 7px;
    vertical-align: middle;
}

.s4-img {
    position: absolute;
    left: -41%;
    bottom: -5%;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .15);
    width: 90%;
}

.s5 .title p {
    width: 80%;
    margin: auto;
}

.conveuer-block {
    width: 100vw;
    align-self: center;
    margin-bottom: 80px;
    overflow: visible;
}

.conveer-line {
    z-index: 2;
    margin-top: 27px;
    margin-bottom: 64px;
    display: flex;
    position: relative;
}

.conveer-elements {
    z-index: 5;
    width: 100vw;
    flex: none;
    display: flex;
}

.conveer-element {
    text-align: center;
    flex: 1;
    font-size: 18px;
    font-weight: 700;
}

.conveer-img-core {
    height: 100px;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
    display: flex;
}

.conveer-element-img {
    object-fit: contain;
    margin-bottom: 30px;
}

.conveuer-block {
    width: 100vw;
    align-self: center;
    margin-bottom: 80px;
    overflow: visible;
}

.conveer-animated-basis {
    z-index: 1;
    max-width: 100vw;
    justify-content: flex-end;
    align-items: center;
    margin-top: -180px;
    display: flex;
    position: relative;
    overflow: visible;
}

p:empty {
    display: none;
}

.s6 {
    background-color: rgb(52, 58, 70);
}

.s6 .box-image {
    margin: 0;
    min-height: 45px;
}

.s6 * {
    color: #fff;
}

.s7-ab {
    position: absolute;
    right: 0;
    top: 0;
    width: 34%;
}

.s8:before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 30px;
    background: transparent linear-gradient(0deg, transparent 0, rgba(14, 25, 36, 0.07) 100%);
}

.s8 .slider-wrapper {
    position: relative;
    padding: 27px;
    border-radius: 16px;
    box-shadow: -5px -5px 10px #fff, 5px 5px 10px rgba(36, 65, 93, 0.33);
    background: #f0f0f3;
}

.s8-right .col-inner {
    position: relative;
    padding: 27px;
    border-radius: 16px;
    box-shadow: -5px -5px 10px #fff, 5px 5px 10px rgba(36, 65, 93, 0.33);
    background: #f0f0f3;
}

.s8 .flickity-page-dots .dot.is-selected {
    background-color: #c62128;
    border-color: #c62128;
    opacity: 1 !important;
}

.slider-nav-dots-square .flickity-page-dots .dot {
    border-radius: 0;
}

.s8 .flickity-page-dots .dot {
    border-radius: 0 !important;
}

.itcus {
    text-align: center;
    margin-bottom: 13px;
}

.itcus p {
    font-size: 14px;
    color: var(--color-body);
}

.itcus span {
    display: block;
    font-size: 25px;
    color: #cc262a;
    margin-bottom: 0;
    font-weight: bold;
    font-family: 'Montserrat', sans-serif;
}

.itform .wpforms-form {
    max-width: 80%;
    margin: 0 auto;
}

.itform .wpforms-container-full input[type=text],
.itform .wpforms-container-full input[type=tel],
.itform .wpforms-container-full input[type=url],
.itform .wpforms-container-full input[type=email] {
    background: transparent;
    width: 100% !important;
    height: 46px;
    box-sizing: border-box;
    padding: 7px 10px;
    border-radius: 16px;
    border: 2px solid #a7a9ac;
    color: #222;
    position: relative;
    z-index: 2;
}

.itform .wpforms-container-full [type=submit] {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    background-color: var(--color-primary) !important;
    margin: 20px auto 50px;
    display: block;
    border-radius: 30px;
}

.badge-frame .badge-inner,
.badge-outline .badge-inner {
    background: var(--color-primary);
    color: #fff;
    border: 2px solid var(--color-primary);
}

.has-hover:hover .badge-outline .badge-inner {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: #fff;
}


/* Inner Page */
.piv1-top h1 {
    font-size: 52px;
    line-height: 107%;
}

.piv1-top h1 span {
    color: #de1820
}

.piv1-bot .nav.nav-vertical {
    height: 210px;
    background-color: rgba(255, 255, 255, .3);
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    flex-direction: column;
    flex: none;

    display: flex;
    overflow: hidden;
}

.piv1-bot .tab-panels {
    border: none;
    background: #fff;
    background-color: #fff;
    border-radius: 6px;
    padding: 40px;
}

.piv1-bot .tab.active {
    color: #e51b23;
    background-color: #ccc;
    border-color: rgba(52, 58, 70, .1);
    border-right-style: none;

}

.piv1-bot .nav>li>a {
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
}

.piv1-bot .nav-vertical .tab {
    max-width: 300px;
    color: transparent;
    padding-left: 30px;
    font-weight: 700;
    transition: all .3s;
}

#tab_additional-info p {
    font-size: 15px;
}

.apis {
    width: 100%;

    justify-content: space-around;
    display: flex;
}

.api-card {
    max-width: 425px;
    min-height: 150px;
    min-width: 310px;
    border: 1px solid #cdcbd5;
    border-radius: 6px;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    margin-bottom: 40px;
    padding: 0 15px;
    margin: 0 10px;
    flex: 1;

    display: flex;
}

.api-card p {
    font-size: 14px;
}

.api-card-heading {
    color: #e51b23;
    text-align: center;
    letter-spacing: 5px;
    text-transform: uppercase;
    background-color: #fff;
    margin-top: -12px;
    padding-left: 20px;
    padding-right: 20px;
}

.buts-line.api-buts {
    margin-bottom: -22px;
    display: flex;
}

.api-buts .button {
    color: #fff;
    letter-spacing: 1px;
    text-transform: uppercase;
    background-color: #343a46;
    border-radius: 6px;
    padding: 17px 12px;
    font-size: 16px;
    margin: 0;
    font-weight: 700;
    line-height: 1em;
    transition: all .3s;
    box-shadow: 0 20px 30px -10px rgba(52, 58, 70, .6);
    /* padding-left: 25px; */
    /* padding-right: 25px; */
    font-size: 14px;
    font-weight: 400;
}

.button.small.margin-right-10 {
    margin-right: 10px;
}

.piv1 .tabbed-content {
    /*flex-flow: inherit;*/
}

.tab-ind .tab a {
    box-shadow: none;
    color: #343a46;
    text-align: center;
    background-color: transparent;
    border-bottom: 2px solid #ebeced;
    border-radius: 0;
    padding: 20px 10px;
    font-size: 13px !important;
    line-height: 1.12em;
    transform: translate(0);
    width: 100%;
    text-align: center;
    display: block;
}

.tab-ind .tab {
    width: 20%;
    margin: 0;
}

.piv1-bot .img {
    background-image: linear-gradient(83.13deg, #484e58, #343a46);
    border-radius: 6px;
    padding: 10px 20px;
}

.tab-ind .tabbed-content {
    padding: 0px 0 !important;
}

.tab-ind .col {
    padding: 0 8px;
}

.tab-ind .box {
    position: relative;
    border-radius: 10px;
    align-items: center;
    margin-bottom: 20px;
    margin-right: 2%;
    padding: 25px;
    cursor: pointer;

    display: flex;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px 2px rgba(52, 58, 70, .1);
}

.tab-ind .box .box-text {
    position: absolute;
    left: 0;
    top: 0;
    background: #343a46;
    opacity: 0;
    visibility: hidden;
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
}

.piv1-bot .tab-ind .tab-panels {
    padding: 20px 0;
}

.tab-ind .box:hover .box-text {
    opacity: 1;
    visibility: initial;

}

.tab-ind .box:hover h4 {
    color: #fff !important;
}

.ct1 h3 {
    color: #333333;
    font-size: 40px;
    margin: 0;
    border: none;
}

.ct1 .section-title-main {
    text-transform: initial;
    border-bottom: 2px solid #FBB648;
}

.ct1-left {
    padding-right: 150px;
}

.ct1-right .img-inner {
    border-radius: 20px;
}

.titleducc b {
    display: none;
}

.titleducc .section-title-main {
    display: table;
    margin: auto;
    position: relative;
    padding-bottom: 15px;
}

.titleducc .section-title-main:before {
    position: absolute;
    content: "";
    display: inline-block;
    height: 5px;
    background: #fab547;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
}

.titleducc {
    margin-bottom: 0;
}

.pp1 h1 {
    font-size: 48.83px;
    margin-bottom: 16px;
}

.pp1 h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 25px;
    line-height: 1.3;
    margin-top: 0;
    margin-bottom: 10px;
}

.pp1 p {
    margin-top: 20px;
}

.pp2 h3 {
    min-height: 65px;
}

.pp2 h2,
.pp3 h2 {
    font-size: 35px;
    width: 50%;
    margin: 0 auto 20px;
}

.pp3 li {
    font-size: 16px;
    line-height: 1.75;
    list-style-type: none;
    position: relative;
    padding-left: 32px;
    margin-bottom: 16px;
    background: url(img/check.svg) 0 0 no-repeat;
    background-size: 24px 24px;
}

.ef1 .col.medium-4 .col-inner {
    position: relative;
    padding: 27px;
    border-radius: 16px;
    box-shadow: -5px -5px 10px #fff, 5px 5px 10px rgba(36, 65, 93, 0.33);
    background: #f0f0f3;
}

.ef1 h4 {
    font-size: 25px;
    margin: 30px 0 10px 0;
}

.ef1 .box-image:before,
.ef1 .box-image:after {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    width: 110%;
    height: 110%;
    border-radius: 85px;
}

.ef1 .box-image:before {
    left: 0;
    top: 0;
    box-shadow: 4px 4px 10px inset rgba(36, 65, 93, 0.19);
}

.ef1 .box-image:after {
    right: 0;
    bottom: 0;
    box-shadow: -4px -4px 10px inset rgba(255, 255, 255, 0.4);
}

.ef2-ab {
    position: absolute;
    top: 0;
    right: 15px;
    z-index: 999;
    margin-top: 0;
    right: -60px;
    padding: 30px 20px;
    max-width: none;
    top: -25px;
    border-radius: 6px;
    box-shadow: 0 12px 36px #0E234380;
    background: #fff;
    display: inline-block;
    width: 370px;
    text-align: center;
}

.ef2 .b-info-pop__title {
    font-size: 25px;
    line-height: 1.3;
    margin-top: 0;
    margin-bottom: 10px;
}

.ef2 .b-info-pop__item {
    margin-bottom: 16px;
}

.ef2 .g-text--highlight {
    font-weight: bold;
    color: #cc262a;
    font-size: 39.06px;
    margin-bottom: 3px;
    line-height: 1.3;
    margin-top: 0;
}

.ef2 .g-text {
    font-size: 16px;
    line-height: 1.75;
    margin-bottom: 26px;
}

.g-button {
    background: #2e75df linear-gradient(281deg, #2e75df 0, #4ca6f3 100%);
    display: inline-block;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    min-width: 255px;
    padding: 17px 15px 16px;
    cursor: pointer;
    border-radius: 16px;
    border: 4px solid #f0f0f3;
    box-shadow: -5px -5px 10px #fff, 5px 5px 10px rgba(36, 65, 93, 0.33);
    transition: all .18s ease-in 0s;
    position: relative;
    text-decoration: none;
}

.g-button:before {
    content: '';
    display: inline-block;
    position: absolute;
    z-index: 0;
    left: -4px;
    top: -4px;
    width: calc(100% + 2 * 4px);
    height: calc(100% + 2 * 4px);
    border-radius: 16px;
    background: transparent;
    border: 0 solid #fff;
    transition: all .18s ease-in 0s;
}

.g-button:hover,
.g-button:focus {
    color: #ffb0b0 !important;
    text-decoration: none;
}

.g-button:hover:before,
.g-button:focus:before {
    /*background: #fff;*/
    border: 4px solid #cc262a;
}

.ef2 .row {
    margin-bottom: 30px;
}

.ef2-3 {
    margin-top: 80px;
}

.ef1 .ef1-dup h4 {
    font-size: 22px;
}

.ef1 .ef1-dup .box p {
    min-height: 300px;
}

.b-fulfillment-info__switchers {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 9px;
    justify-content: left;

}

.b-fulfillment-info__switcher p {
    margin-bottom: 0 !important;
}

.b-fulfillment-info__switcher {
    display: flex;
    align-items: center;
    min-width: 208px;
    margin-bottom: 20px;
}

.b-fulfillment-info__switcher {
    margin-bottom: 23px;
}

.b-fulfillment-info__switcher-wrapper {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 27px;
    margin-right: 9px;
    margin-bottom: 0;
}

.b-fulfillment-info__switcher-wrapper input {
    opacity: 0;
    width: 0;
    height: 0;
}

.b-fulfillment-info__switcher-slider {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 27px;
    background-color: #cc262a;
    box-shadow: 0 0 5px inset rgba(36, 65, 93, 0.5);
    transition: .4s;
}

.b-fulfillment-info__switcher-slider:before {
    position: absolute;
    content: "";
    height: 23px;
    width: 23px;
    left: 2px;
    bottom: 2px;
    border-radius: 50%;
    background-color: #f0f0f3;
    box-shadow: 1px 1px 7px rgba(27, 67, 126, 0.5);
    transition: .4s;
}

.b-fulfillment-info__switcher-input:checked+.b-fulfillment-info__switcher-slider {
    background-color: #54aefb;
}

.b-fulfillment-info__switcher-input:checked+.b-fulfillment-info__switcher-slider:before {
    transform: translateX(33px);
}

.efmp {
    position: relative;
    padding: 27px;
    border-radius: 16px;
    box-shadow: -5px -5px 10px #fff, 5px 5px 10px rgba(36, 65, 93, 0.33);
    background: #f0f0f3;
}

.efmp .col {
    padding-bottom: 0;
}

.drag_element img {
    position: absolute;
    top: 40%;
    right: 37%;
}

.drag_element img:nth-child(2) {
    top: 36%;
    right: 71%;
}

.drag_element img:nth-child(3) {
    top: 66%;
    right: 69%;
}

@media screen and (max-width: 1024px) {
    .ct1-left {
        padding-right: 15px !important;
    }

}

/* ----------------------------------------------------
4. FOOTER
----------------------------------------------------- */
.site-footer {
    color: #fff;
    background-color: var(--color-dark) !important;
}

.site-footer .section-title-main {
    text-transform: uppercase;
}

/* ----------------------------------------------------
4. WIDGETS & PLUGINS
----------------------------------------------------- */


@media screen and (max-width: 767px) {
    body {
        position: relative;
        overflow: hidden;
        ;
    }

    .conveer-animated-basis {
        max-width: 98vw;
    }

    .s2 {
        padding: 15px 7.5px 0 7.5px !important;
    }

    .title h2 {
        font-size: 20px;
    }

    .s2 .col.medium-3 {
        flex-basis: 50%;
        max-width: 50%;
        margin-bottom: 15px;
        padding: 0 7.5px;
    }

    .s2 .col.small-12 {
        padding-bottom: 0;
    }

    .title p {
        text-align: center;
        font-size: 14px;
    }

    .s2 .box {
        padding: 10px 0;
    }

    .s2 .box-text {
        padding-bottom: 0;
        min-height: 80px;
    }

    .s3 {
        padding: 0 !important;
    }

    .s3-left {
        padding: 0 15px 15px 15px !important
    }

    .s3-right h2 {
        font-size: 23px;
    }

    .s3-right p {
        font-size: 14px;
        min-height: 70px;
    }

    .s3-right .col {
        flex-basis: 50%;
        max-width: 50%;
        padding: 0 7.5px;
        margin-bottom: 15px;
    }

    .s3 .box {
        margin: 0;
    }

    .s3-right .row {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .s3 .box h4 {
        min-height: 37px;
    }

    .s4 {
        padding: 15px 0 0 0 !important;
    }

    .s4-left h2 {
        font-size: 22px;
    }

    .s4-left p,
    .s4-left li {
        font-size: 14px;
    }

    .s3-img {
        left: 50%;
        transform: translateX(-50%);
    }

    .s5 {
        padding: 0 !important;
    }

    .s5 .title p {
        width: 100%;
    }

    .conveer-element {
        margin: 0 20px;
    }

    .conveer-line {
        margin-top: 0;
    }

    .s6 {
        padding: 0 !important;
    }

    .s6 h2 {
        font-size: 22px;
    }

    .s6 p {
        font-size: 14px;
    }

    .s6-left .col.medium-6 {
        flex-basis: 50%;
        max-width: 50%;
    }

    .s6 .img img {
        margin: -75px 0 0
    }

    .s7-ab {
        width: 85%;
        top: 15px;
    }

    .s7 .button {
        font-size: 13px;
    }

    .s7 {
        padding: 0 !important;
    }

    .s8 {
        padding: 15px 0 !important;
    }

    .s8 .wpcf7 {
        padding: 0;
    }

    .s7-ab p {
        font-size: 14px;

    }

    .s8-right .col-inner {
        padding: 15px;
    }

    .s8 .slider-wrapper h4 {
        font-size: 20px;
    }

    .s8-right h3 {
        font-size: 22px;
    }

    .s8-right p {
        font-size: 14px;
    }

    .itform .wpcf7-form-control-wrap {

        margin: 3px;
    }

    .itform .wpcf7-form-control-wrap input {
        margin: 0;
        font-size: 14px;
    }

    .s8 .wpcf7 .wpcf7-submit {
        margin: 20px auto;
    }

    .s8-right {
        padding-bottom: 0;
    }

    #footer .col.small-6 {
        flex-basis: 100%;
        max-width: 100%;
    }

    #footer .col.small-6:first-child .img {
        width: 20%;
        margin: auto;
        display: none;
    }

    #footer .col.small-6:nth-child(3) .img {
        width: 35%;
    }

    .ct1 h3 {
        font-size: 22px;
    }

    .ct2 {
        padding-bottom: 0 !important;
    }

    .pp1 {
        padding: 15px 0 !important;
    }

    .pp1 .col:first-child {
        order: 1;
    }

    .pp1 .col:nth-child(2) {
        order: 2;
    }

    .pp1 h1 {
        font-size: 30px;
    }

    .pp1 h2 {
        font-size: 18px;
    }

    .pp1 p {

        font-size: 14px;
    }

    .ef1 {
        padding: 15px 0 !important;
    }

    .ef1 .text img {
        margin-bottom: 0;
    }

    .ef1 p {
        font-size: 14px;
    }

    .ef1 h4 {
        font-size: 20px;
    }

    .ef1 {
        margin-bottom: -50px;
    }

    .efmp img {
        margin-bottom: 0 !important;
    }

    .efmp {
        padding: 15px 0;
    }

    .text {
        font-size: 14px;
    }

    .ef2 {
        padding: 0 !important;
    }

    .ef2 img {
        margin-bottom: 0 !important
    }

    .ef2 p {
        font-size: 14px;
    }

    .ef2-mid .col:nth-child(2) {
        order: 1;
    }

    .ef2-mid .col:nth-child(1) {
        order: 2;
    }

    .ef2-mid {
        margin-bottom: 0 !important;
    }

    .ef2-3 {
        margin-top: 15px;
    }

    .ef2-ab {
        position: inherit;
        width: 100%;
        right: initial;
        top: initial;
        margin-top: 45px;
    }

    .ef2 .b-info-pop__title {
        font-size: 22px;
    }

    .ef2 .g-text--highlight {
        font-size: 30px;
    }

    .row.ef2-3 {
        margin: 0;
    }

    .ef1 .ef1-dup .box p {
        min-height: auto;
    }

    .ef1 .ef1-dup h4 {
        font-size: 20px;
    }

    .ef1 .col.medium-4 .col-inner {
        padding: 15px;
    }


    .pp1 .of1 .col.medium-6:nth-child(2) {
        padding-right: 15px;
    }

    .of1 h3 {
        font-size: 22.25px;
    }

    .of2 .col.large-12 h3 {
        font-size: 22.25px;
    }

    .of2 {
        padding-bottom: 0 !important;
    }

    .off2 {
        margin-top: 80px;
    }

    .off2 h3 {
        font-size: 22px;
    }

    .off2 ul {
        width: 100%;
    }

    .off2 li {
        font-size: 14px;
        margin-left: 0 !important;
    }

    .off3 {
        padding: 15px 0 !important;
    }

    .off3 h2 {
        font-size: 22px;
    }


    .off3-mid .col:nth-child(2) {
        order: 1;
    }

    .off3-mid .col:nth-child(1) {
        order: 2;
    }

    .off3 img {
        margin: -10px 0 0px 0;
    }

    .pls1 h4 {
        font-size: 20px;
    }

    .pls2 h2 {
        font-size: 22px;
    }

    .pls2 .col {
        padding-bottom: 0;
    }

    .pls2 {
        padding: 15px 0 0 0 !important;
    }

    .pls2 .text img {
        margin-bottom: 0 !important;
    }

    .of2 {
        padding: 15px 0 !important;
    }

    .pls3 {
        padding: 15px;
    }

    .pls3 h3 {
        font-size: 22px;
    }

    .pls3 .img {
        width: 40% !important;
        margin: auto;
    }

    .pls4 {
        padding: 15px 0 !important;
    }

    .pls5 h3 {
        font-size: 22px;
    }

    .ws2 h2 {
        font-size: 22px;

    }

    .ws3 p {
        font-size: 14px;
    }

    .ws3-mid .col:nth-child(2) {
        order: 1;
    }

    .ws3-mid .col:nth-child(1) {
        order: 2;
    }

    .pp2 h2,
    .pp3 h2 {
        width: 100%;
        font-size: 22px;
    }

    .pp2 h3 {
        font-size: 15px;
    }

    .pp2 h3 {
        min-height: auto;
    }

    .pp2 .col {
        padding-bottom: 0;
    }

    .pp3 li {
        font-size: 14px;
        margin-left: 0 !important;
    }

    .pp3 .col {
        padding-bottom: 0;
    }

    .pp3 {
        padding-bottom: 0 !important;
    }

    .piv1-top h1 {
        font-size: 26px;
        line-height: 1.4;
    }

    .piv1 {
        padding: 15px 0 !important;
    }

    .piv1 .col {
        padding-bottom: 0;
    }

    .piv1 .col p {
        font-size: 14px;
    }

    .piv1-bot .nav-vertical .tab {
        width: 100%;
        max-width: 100%;
        font-size: 14px;
        padding: 0;
    }

    .piv1-bot .nav-vertical .tab a {
        display: block;
        text-align: center;
        color: #000;
        text-transform: initial;
    }

    .piv1-bot .tab-panels {
        padding: 15px;
    }

    .piv1-bot .nav.nav-vertical {
        height: auto;
        margin-bottom: 15px;
    }

    .piv1-bot ul.nav {
        display: block;
    }

    .tab-ind .tab a {
        text-transform: inherit;
        padding: 15px 20px;
    }

    .tab-ind .tab {
        width: auto;
    }

    .piv1-bot .tab-ind .tab-panels {
        padding: 15px;
    }

    .tab-ind .box {
        padding: 10px;
    }

    .conveer-animated-basis {
        margin-top: -97px;
    }

    .conveer-element-img {
        margin-bottom: -15px;
    }

    .conveer-element {
        font-size: 16px;
    }

    .conveer-img-core {
        height: 75px;
    }

    .conveer-animated-basis {
        position: unset;
        overflow: initial;
    }

    .conveer-line {
        margin-bottom: 170px;
    }

    .conveuer-block {
        margin-bottom: 30px;
    }
}