<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fulfillment Switcher</title>
    <style>
        /* Reset và base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 40px 20px;
        }

        /* Container styles */
        .b-fulfillment-info__switchers {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Individual switcher */
        .b-fulfillment-info__switcher {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .b-fulfillment-info__switcher:last-child {
            border-bottom: none;
        }

        /* Text styles */
        .g-text {
            font-size: 16px;
            color: #333;
            cursor: pointer;
            user-select: none;
        }

        .g-text--bold {
            font-weight: 600;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        /* Switch wrapper */
        .b-fulfillment-info__switcher-wrapper {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            cursor: pointer;
        }

        /* Hide default checkbox */
        .b-fulfillment-info__switcher-input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        /* Switch slider */
        .b-fulfillment-info__switcher-slider {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            border-radius: 34px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .b-fulfillment-info__switcher-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Active state */
        .b-fulfillment-info__switcher-input:checked + .b-fulfillment-info__switcher-slider {
            background-color: #4CAF50;
        }

        .b-fulfillment-info__switcher-input:checked + .b-fulfillment-info__switcher-slider:before {
            transform: translateX(26px);
        }

        /* Hover effects */
        .b-fulfillment-info__switcher-slider:hover {
            box-shadow: 0 0 0 8px rgba(76, 175, 80, 0.1);
        }

        .b-fulfillment-info__switcher-input:checked + .b-fulfillment-info__switcher-slider:hover {
            background-color: #45a049;
        }

        /* Focus styles */
        .b-fulfillment-info__switcher-input:focus + .b-fulfillment-info__switcher-slider {
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
        }

        /* Disabled state */
        .b-fulfillment-info__switcher-input:disabled + .b-fulfillment-info__switcher-slider {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Animation for smooth transitions */
        .b-fulfillment-info__switcher {
            transition: all 0.2s ease;
        }

        .b-fulfillment-info__switcher:hover {
            background-color: #f9f9f9;
            border-radius: 8px;
            margin: 0 -10px;
            padding: 15px 10px;
        }

        /* Title */
        .switcher-title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 24px;
            font-weight: 700;
        }

        /* Status indicator */
        .status-indicator {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .status-text {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="switcher-title">Chọn Quốc Gia Fulfillment</div>
    
    <div class="b-fulfillment-info__switchers">
        <div class="b-fulfillment-info__switcher">
            <label class="g-text g-text--bold mb-0" for="js-b-fulfillment-info__switcher-input--philippines">Philippines</label>
            <label class="b-fulfillment-info__switcher-wrapper">
                <input id="js-b-fulfillment-info__switcher-input--philippines" 
                       class="b-fulfillment-info__switcher-input" 
                       type="checkbox" 
                       checked 
                       autocomplete="off">
                <span class="b-fulfillment-info__switcher-slider"></span>
            </label>
        </div>

        <div class="b-fulfillment-info__switcher">
            <label class="g-text g-text--bold mb-0" for="js-b-fulfillment-info__switcher-input--thailand">Thailand</label>
            <label class="b-fulfillment-info__switcher-wrapper">
                <input id="js-b-fulfillment-info__switcher-input--thailand" 
                       class="b-fulfillment-info__switcher-input" 
                       type="checkbox" 
                       checked 
                       autocomplete="off">
                <span class="b-fulfillment-info__switcher-slider"></span>
            </label>
        </div>

        <div class="b-fulfillment-info__switcher">
            <label class="g-text g-text--bold mb-0" for="js-b-fulfillment-info__switcher-input--malaysia">Malaysia</label>
            <label class="b-fulfillment-info__switcher-wrapper">
                <input id="js-b-fulfillment-info__switcher-input--malaysia" 
                       class="b-fulfillment-info__switcher-input" 
                       type="checkbox" 
                       autocomplete="off">
                <span class="b-fulfillment-info__switcher-slider"></span>
            </label>
        </div>
    </div>

    <div class="status-indicator">
        <div class="status-text" id="status-text">
            Đã chọn: Philippines, Thailand
        </div>
    </div>

    <script>
        // JavaScript để handle switching logic
        document.addEventListener('DOMContentLoaded', function() {
            const switchers = document.querySelectorAll('.b-fulfillment-info__switcher-input');
            const statusText = document.getElementById('status-text');

            function updateStatus() {
                const selectedCountries = [];
                switchers.forEach(switcher => {
                    if (switcher.checked) {
                        const label = document.querySelector(`label[for="${switcher.id}"]`);
                        selectedCountries.push(label.textContent);
                    }
                });

                if (selectedCountries.length > 0) {
                    statusText.textContent = `Đã chọn: ${selectedCountries.join(', ')}`;
                } else {
                    statusText.textContent = 'Chưa chọn quốc gia nào';
                }
            }

            // Add event listeners
            switchers.forEach(switcher => {
                switcher.addEventListener('change', function() {
                    updateStatus();
                    
                    // Optional: Add animation effect
                    this.closest('.b-fulfillment-info__switcher').style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.closest('.b-fulfillment-info__switcher').style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Initialize status
            updateStatus();
        });
    </script>
</body>
</html>
