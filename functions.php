<?php

// Disable <PERSON><PERSON> for posts
add_filter('use_block_editor_for_post', '__return_false', 10);

// Disable <PERSON><PERSON> for post types
add_filter('use_block_editor_for_post_type', '__return_false', 10);

// Disable Gutenberg widgets
add_filter('use_widgets_block_editor', '__return_false');

// Remove Gutenberg CSS
function vietfulfil_remove_gutenberg_css() {
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-blocks-style'); // Remove WooCommerce block CSS
}
add_action('wp_enqueue_scripts', 'vietfulfil_remove_gutenberg_css', 100);

// Remove Gutenberg from admin
function vietfulfil_remove_gutenberg_admin() {
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
}
add_action('admin_enqueue_scripts', 'vietfulfil_remove_gutenberg_admin', 100);
